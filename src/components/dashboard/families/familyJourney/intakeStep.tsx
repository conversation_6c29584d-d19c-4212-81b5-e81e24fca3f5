import { useState } from 'react';
import FamilyInformationStep from './intakeStep/familyInformationStep';
import AssignmentConfirmationStep from './intakeStep/assignmentConfirmationStep';
import IntakeMeetingStep from './intakeStep/intakeMeetingStep';

interface Child {
  name: string;
  age: string;
  gender: string;
}

interface FamilyFormData {
  familyName: string;
  address: string;
  postalCode: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  children: Child[];
  preferredStartDate: string;
  latestStartDate: string;
}

export default function IntakeStep() {
  const [familyData, setFamilyData] = useState<FamilyFormData>({
    familyName: '',
    address: '',
    postalCode: '',
    city: '',
    country: '',
    phone: '',
    email: '',
    children: [],
    preferredStartDate: '',
    latestStartDate: '',
  });

  const [familyInfoCompleted, setFamilyInfoCompleted] = useState(false);
  const [assignmentCompleted, setAssignmentCompleted] = useState(true);
  const [intakeMeetingCompleted, setIntakeMeetingCompleted] = useState(true);

  const handleFamilyInfoSubmit = (data: FamilyFormData) => {
    setFamilyData(data);
    setFamilyInfoCompleted(true);
    console.log('Family information submitted:', data);
  };

  const handleAssignmentConfirmation = () => {
    setAssignmentCompleted(true);
    console.log('Assignment confirmation completed');
  };

  const handleIntakeMeetingConfirmation = () => {
    setIntakeMeetingCompleted(true);
    console.log('Intake meeting completed');
  };
  return (
    <div className="space-y-8">
      <FamilyInformationStep
        formData={familyData}
        setFormData={setFamilyData}
        onSubmit={handleFamilyInfoSubmit}
        isCompleted={familyInfoCompleted}
      />

      <AssignmentConfirmationStep
        onSubmit={handleAssignmentConfirmation}
        isCompleted={assignmentCompleted}
      />

      <IntakeMeetingStep
        onSubmit={handleIntakeMeetingConfirmation}
        isCompleted={intakeMeetingCompleted}
      />
    </div>
  );
}
